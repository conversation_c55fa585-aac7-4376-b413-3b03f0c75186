"use client";

import React from 'react';
import { useSeasonInfo } from '@/lib/hooks/useSeasonInfo';
import { Skeleton } from '@/components/ui/LoadingState';

/**
 * Calendar title component with season info and bye status
 */
export function CalendarTitle() {
  const { data: seasonInfo, isLoading, error } = useSeasonInfo();

  if (error) {
    // Fallback to basic title if there's an error
    return <h1 className="text-2xl sm:text-3xl font-bold">Calendario Tornei</h1>;
  }

  if (isLoading) {
    return (
      <div className="space-y-2">
        <Skeleton className="h-8 w-64" />
        <Skeleton className="h-5 w-48" />
      </div>
    );
  }

  const title = seasonInfo?.name 
    ? `Calendario Tornei - ${seasonInfo.name}`
    : 'Calendario Tornei';

  const byeStatusText = seasonInfo?.byeStatus.achieved 
    ? `Status acquisizione Bye: Raggiunto (${seasonInfo.byeStatus.average})`
    : `Status acquisizione Bye: Non raggiunto (${seasonInfo?.byeStatus.average || 0})`;

  const byeStatusColor = seasonInfo?.byeStatus.achieved 
    ? 'text-lime-400' 
    : 'text-red-400';

  return (
    <div className="space-y-1">
      <h1 className="text-2xl sm:text-3xl font-bold">
        {title}
      </h1>
      {seasonInfo && (
        <p className={`text-sm sm:text-base ${byeStatusColor} font-medium`}>
          {byeStatusText}
        </p>
      )}
    </div>
  );
}
