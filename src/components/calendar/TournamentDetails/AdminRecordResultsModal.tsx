"use client";

import React, { useState, useEffect } from 'react';
import { X, Save, AlertCircle, FileText, Calendar as CalendarIcon, MapPin, CheckCircle } from 'lucide-react';
import { Tournament } from '@/types/calendar';
import { Colors } from '@/lib/constants/colors';
import { format } from 'date-fns';
import { it } from 'date-fns/locale';
import { Spinner } from '@/components/ui/Spinner';
import { useTournamentRegistrations } from '@/lib/hooks/useRegistration';
import { useTournamentResults } from '@/lib/hooks/useTournamentResults';
import { useRecordResults } from '@/hooks/useRecordResults';
import { useLockBodyScroll } from '@/lib/hooks/useLockBodyScroll';

interface PlayerResult {
  playerId: string;
  playerName: string;
  archetypeId: string;
  archetypeName: string;
  deckName: string;
  deckListUrl: string;
  position: number;
  points: number;
}

interface AdminRecordResultsModalProps {
  tournament: Tournament;
  isOpen: boolean;
  onClose: () => void;
}

interface SaveConfirmationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  tournament: Tournament;
  resultsCount: number;
  isUpdate: boolean;
  isLoading: boolean;
}

function SaveConfirmationModal({
  isOpen,
  onClose,
  onConfirm,
  tournament,
  resultsCount,
  isUpdate,
  isLoading
}: SaveConfirmationModalProps) {
  if (!isOpen) return null;

  const storeColor = Colors.getStoreScheme(tournament.store, tournament);

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-[60] flex items-center justify-center p-4">
      <div className="bg-gradient-to-br from-blue-950 via-sky-900 to-blue-900 rounded-xl border border-blue-500/30 w-full max-w-md overflow-hidden">
        {/* Header */}
        <div className="p-4 border-b border-blue-500/20 flex justify-between items-center">
          <div className="flex items-center gap-3">
            <div className={`p-2 rounded-lg ${storeColor.bg}`}>
              <CheckCircle size={20} className={storeColor.text} />
            </div>
            <h3 className="text-lg font-bold text-white">Conferma Salvataggio</h3>
          </div>
          {!isLoading && (
            <button
              onClick={onClose}
              className="p-1 hover:bg-blue-900/50 rounded-full transition-colors"
            >
              <X size={18} className="text-blue-300 hover:text-white" />
            </button>
          )}
        </div>

        {/* Content */}
        <div className="p-4 space-y-4">
          <div className="text-center">
            <p className="text-white text-lg mb-2">
              Sei sicuro di voler {isUpdate ? 'aggiornare' : 'salvare'} i risultati?
            </p>
            <p className="text-blue-300 text-sm">
              Torneo: <span className="font-medium">{tournament.title}</span>
            </p>
            <p className="text-blue-300 text-sm">
              Risultati: <span className="font-medium">{resultsCount} giocatori</span>
            </p>
          </div>

          {isUpdate && (
            <div className="p-3 bg-amber-900/20 border border-amber-500/30 rounded-lg">
              <div className="flex items-center gap-2">
                <AlertCircle size={16} className="text-amber-400" />
                <p className="text-amber-300 text-sm">
                  I risultati esistenti verranno sostituiti
                </p>
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex gap-3 p-4 border-t border-blue-500/20">
          <button
            onClick={onClose}
            disabled={isLoading}
            className="flex-1 px-4 py-2 text-blue-300 hover:text-white transition-colors disabled:opacity-50"
          >
            Annulla
          </button>
          <button
            onClick={onConfirm}
            disabled={isLoading}
            className={`flex-1 flex items-center justify-center gap-2 px-4 py-2 ${storeColor.bg} ${storeColor.bgHover} disabled:opacity-50 disabled:cursor-not-allowed ${storeColor.text} rounded-lg font-medium transition-colors`}
          >
            {isLoading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-current" />
                Salvataggio...
              </>
            ) : (
              <>
                <Save size={16} />
                Conferma
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
}

export function AdminRecordResultsModal({
  tournament, 
  isOpen, 
  onClose 
}: AdminRecordResultsModalProps) {
  const [results, setResults] = useState<PlayerResult[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showConfirmation, setShowConfirmation] = useState(false);

  const { data: registrations, isLoading: isLoadingRegistrations } = useTournamentRegistrations(tournament.id, tournament, true);
  const { data: existingResults, isLoading: isLoadingResults } = useTournamentResults(tournament.id);
  const recordResults = useRecordResults();

  // Lock body scroll when modal is open
  useLockBodyScroll(isOpen);

  // Initialize results when data loads
  useEffect(() => {
    if (!registrations || isLoadingRegistrations) return;

    if (existingResults && existingResults.length > 0) {
      // Load existing results
      const loadedResults = existingResults.map((result) => {
        const registration = registrations.find(reg => reg.player_id === result.player_id);
        return {
          playerId: result.player_id,
          playerName: result.player?.name || '',
          archetypeId: registration?.archetype_id || '',
          archetypeName: registration?.archetypes?.name || '',
          deckName: registration?.deck_name || '',
          deckListUrl: registration?.deck_list_url || '',
          position: result.position,
          points: result.points
        };
      }).sort((a, b) => a.position - b.position);
      setResults(loadedResults);
    } else {
      // Initialize with just one empty row
      setResults([{
        playerId: '',
        playerName: '',
        archetypeId: '',
        archetypeName: '',
        deckName: '',
        deckListUrl: '',
        position: 1,
        points: 0
      }]);
    }
  }, [registrations, existingResults, isLoadingRegistrations]);

  const updatePlayerPosition = (index: number, newPlayerId: string) => {
    const newResults = [...results];
    const wasEmpty = !newResults[index].playerId;

    // If deselecting a player (empty value)
    if (!newPlayerId) {
      newResults[index] = {
        playerId: '',
        playerName: '',
        archetypeId: '',
        archetypeName: '',
        deckName: '',
        deckListUrl: '',
        position: index + 1,
        points: newResults[index]?.points || 0
      };

      setResults(newResults);
      return;
    }

    // If selecting a player
    const registration = registrations?.find(reg => reg.player_id === newPlayerId);
    if (!registration) return;

    newResults[index] = {
      playerId: newPlayerId,
      playerName: registration.players?.name || '',
      archetypeId: registration.archetype_id || '',
      archetypeName: registration.archetypes?.name || '',
      deckName: registration.deck_name || '',
      deckListUrl: registration.deck_list_url || '',
      position: index + 1,
      points: newResults[index]?.points || 0
    };

    // If this was an empty row and we just filled it, add a new empty row
    if (wasEmpty && index === results.length - 1) {
      newResults.push({
        playerId: '',
        playerName: '',
        archetypeId: '',
        archetypeName: '',
        deckName: '',
        deckListUrl: '',
        position: newResults.length + 1,
        points: 0
      });
    }

    setResults(newResults);
  };

  const updatePlayerPoints = (index: number, value: string) => {
    const newResults = [...results];
    if (newResults[index]) {
      // Allow empty string or convert to number, ensuring minimum of 0
      const points = value === '' ? 0 : Math.max(0, parseInt(value) || 0);
      newResults[index].points = points;
      setResults(newResults);
    }
  };

  const getAvailablePlayers = (currentIndex: number) => {
    const usedPlayerIds = results
      .map((result, index) => index !== currentIndex ? result.playerId : null)
      .filter(Boolean);

    return registrations?.filter(reg =>
      !usedPlayerIds.includes(reg.player_id)
    ) || [];
  };

  const handleSave = () => {
    // Filter out empty rows and validate
    const filledResults = results.filter(result => result.playerId);
    if (filledResults.length === 0) {
      setError('Devi inserire almeno un risultato');
      return;
    }

    // Show confirmation modal
    setShowConfirmation(true);
  };

  const handleConfirmSave = async () => {
    const filledResults = results.filter(result => result.playerId);

    setIsLoading(true);
    setError(null);

    try {
      const resultsToSave = filledResults.map(result => ({
        playerId: result.playerId,
        position: result.position,
        points: result.points,
        deckListUrl: result.deckListUrl
      }));

      await recordResults.mutateAsync({
        tournamentId: tournament.id,
        results: resultsToSave
      });

      // Close confirmation modal first
      setShowConfirmation(false);
      // Close main modal on success
      onClose();
    } catch (err) {
      setError('Errore durante il salvataggio dei risultati');
      console.error('Error saving results:', err);
      setShowConfirmation(false);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancelSave = () => {
    setShowConfirmation(false);
  };

  if (!isOpen) return null;

  const storeColor = Colors.getStoreScheme(tournament.store, tournament);

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-gradient-to-br from-blue-950 via-sky-900 to-blue-900 rounded-xl border border-blue-500/30 w-full max-w-2xl max-h-[95vh] sm:max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="p-3 sm:p-4 border-b border-blue-500/20 flex justify-between items-center">
          <div className="flex items-center gap-3">
            <div className={`p-2 rounded-lg ${storeColor.bg}`}>
              <FileText size={20} className={storeColor.text} />
            </div>
            <div>
              <h2 className="text-lg sm:text-xl font-bold text-white">Registra Risultati</h2>
              <p className="text-sm text-blue-300 mt-1">{tournament.title}</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-1 hover:bg-blue-900/50 rounded-full transition-colors"
          >
            <X size={18} className="sm:w-5 sm:h-5 text-blue-300" />
          </button>
        </div>

        {/* Content */}
        <div className="p-3 sm:p-4 overflow-y-auto max-h-[calc(95vh-12rem)] sm:max-h-[calc(90vh-12rem)]">
          {/* Tournament Summary */}
          <div className={`mb-4 p-3 rounded-lg bg-black/30 border ${storeColor.border}`}>
            <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4">
              <div className="flex items-center gap-2">
                <CalendarIcon size={16} className={storeColor.light} />
                <span className="text-sm text-white">{format(new Date(tournament.date), "d MMMM yyyy", { locale: it })}</span>
              </div>
              {tournament.store && (
                <div className="flex items-center gap-2">
                  <MapPin size={16} className={storeColor.light} />
                  <span className="text-sm text-white">{tournament.store.name}</span>
                </div>
              )}
            </div>
          </div>

          {isLoadingRegistrations || isLoadingResults ? (
            <div className="flex items-center justify-center py-8">
              <Spinner />
            </div>
          ) : (
            <div className="space-y-4">
              {results.map((result, index) => {
                const hasPlayer = !!result.playerId;
                const isLastEmptyRow = !hasPlayer && index === results.length - 1;
                const hasAvailablePlayers = getAvailablePlayers(index).length > 0;

                // Show row if:
                // 1. It has a player selected, OR
                // 2. It's the last empty row and there are still players available, OR
                // 3. There are filled rows after this one (meaning this row was created by user interaction)
                const hasFilledRowsAfter = results.slice(index + 1).some(r => !!r.playerId);
                const shouldShow = hasPlayer || (isLastEmptyRow && hasAvailablePlayers) || hasFilledRowsAfter;

                if (!shouldShow) return null;

                return (
                  <div key={index} className={`flex items-center gap-3 sm:gap-4 p-3 sm:p-4 bg-black/30 border ${storeColor.border} rounded-lg`}>
                    <div className="w-8 sm:w-12 text-center">
                      <div className={`w-8 h-8 sm:w-10 sm:h-10 rounded-full ${storeColor.bg} flex items-center justify-center`}>
                        <span className={`text-sm sm:text-lg font-bold ${storeColor.text}`}>{index + 1}</span>
                      </div>
                    </div>

                    <div className="flex-1">
                      <select
                        value={result.playerId}
                        onChange={(e) => updatePlayerPosition(index, e.target.value)}
                        className="w-full px-3 py-2 bg-black/30 border border-blue-500/30 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500/50 text-sm"
                      >
                        <option value="">
                          {result.playerId ? "Rimuovi giocatore..." : "Seleziona giocatore..."}
                        </option>
                        {getAvailablePlayers(index).map(player => (
                          <option key={player.player_id} value={player.player_id}>
                            {player.players?.name} - {player.archetypes?.name || 'Nessun archetipo'}
                          </option>
                        ))}
                      </select>
                    </div>

                    <div className="w-20 sm:w-24">
                      <input
                        type="number"
                        min="0"
                        value={result.points || ''}
                        onChange={(e) => updatePlayerPoints(index, e.target.value)}
                        className="w-full px-2 sm:px-3 py-2 bg-black/30 border border-blue-500/30 rounded-lg text-white text-center focus:outline-none focus:ring-2 focus:ring-blue-500/50 text-sm disabled:opacity-50 disabled:cursor-not-allowed"
                        placeholder="Punti"
                        disabled={!hasPlayer}
                      />
                    </div>
                  </div>
                );
              })}
            </div>
          )}

          {error && (
            <div className="mt-4 p-3 bg-red-900/20 border border-red-500/30 rounded-lg flex items-center gap-2">
              <AlertCircle size={16} className="text-red-400" />
              <span className="text-red-300 text-sm">{error}</span>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex flex-col sm:flex-row items-stretch sm:items-center justify-end gap-2 sm:gap-3 p-3 sm:p-4 border-t border-blue-500/20">
          <button
            onClick={onClose}
            className="px-4 py-2 text-blue-300 hover:text-white transition-colors text-sm sm:text-base"
          >
            Annulla
          </button>
          <button
            onClick={handleSave}
            disabled={isLoading || isLoadingRegistrations || isLoadingResults}
            className={`flex items-center justify-center gap-2 px-4 py-2 ${storeColor.bg} ${storeColor.bgHover} disabled:opacity-50 disabled:cursor-not-allowed ${storeColor.text} rounded-lg font-medium transition-colors text-sm sm:text-base`}
          >
            <Save size={16} />
            {isLoading ? 'Salvataggio...' : 'Salva Risultati'}
          </button>
        </div>
      </div>

      {/* Confirmation Modal */}
      <SaveConfirmationModal
        isOpen={showConfirmation}
        onClose={handleCancelSave}
        onConfirm={handleConfirmSave}
        tournament={tournament}
        resultsCount={results.filter(result => result.playerId).length}
        isUpdate={!!(existingResults?.length)}
        isLoading={isLoading}
      />
    </div>
  );
}
