"use client";

import { useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/lib/supabase/client';
import { logger } from '@/lib/utils/logger';

interface PlayerResult {
  playerId: string;
  position: number;
  points: number;
  deckListUrl?: string;
}

interface RecordResultsParams {
  tournamentId: string;
  results: PlayerResult[];
}

export function useRecordResults() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ tournamentId, results }: RecordResultsParams) => {
      try {
        // First, delete existing results for this tournament
        const { error: deleteError } = await supabase
          .from('tournament_results')
          .delete()
          .eq('tournament_id', tournamentId);

        if (deleteError) {
          throw new Error(`Failed to delete existing results: ${deleteError.message}`);
        }

        // Prepare results data for insertion
        const resultsData = results.map(result => ({
          tournament_id: tournamentId,
          player_id: result.playerId,
          position: result.position,
          points: result.points,
          deck_list_url: result.deckListUrl || null
        }));

        // Insert new results
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const { data, error: insertError } = await (supabase as any)
          .from('tournament_results')
          .insert(resultsData)
          .select();

        if (insertError) {
          throw new Error(`Failed to insert results: ${insertError.message}`);
        }

        logger.info('Tournament results recorded successfully', { 
          tournamentId, 
          resultsCount: results.length 
        });

        return data;
      } catch (error) {
        logger.error('Error recording tournament results', error, { 
          component: 'useRecordResults',
          tournamentId 
        });
        throw error;
      }
    },
    onSuccess: (data, variables) => {
      // Invalidate related queries
      queryClient.invalidateQueries({
        queryKey: ['tournament-results', variables.tournamentId]
      });
      queryClient.invalidateQueries({
        queryKey: ['tournaments', variables.tournamentId]
      });
      queryClient.invalidateQueries({
        queryKey: ['leaderboard']
      });
      
      logger.info('Tournament results cache invalidated', { 
        tournamentId: variables.tournamentId 
      });
    },
    onError: (error, variables) => {
      logger.error('Failed to record tournament results', error, { 
        component: 'useRecordResults',
        tournamentId: variables.tournamentId 
      });
    }
  });
}
